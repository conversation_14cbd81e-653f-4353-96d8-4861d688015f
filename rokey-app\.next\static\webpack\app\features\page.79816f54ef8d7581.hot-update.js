"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/features/page",{

/***/ "(app-pages-browser)/./src/components/landing/LandingNavbar.tsx":
/*!**************************************************!*\
  !*** ./src/components/landing/LandingNavbar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* harmony import */ var _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInstantNavigation */ \"(app-pages-browser)/./src/hooks/useInstantNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LandingNavbar() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dropdownOpen, setDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const toggleDropdown = (dropdown)=>{\n        setDropdownOpen(dropdownOpen === dropdown ? null : dropdown);\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"LandingNavbar.useEffect.handleClickOutside\": ()=>{\n                    setDropdownOpen(null);\n                }\n            }[\"LandingNavbar.useEffect.handleClickOutside\"];\n            if (dropdownOpen) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"LandingNavbar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"LandingNavbar.useEffect\"];\n            }\n        }\n    }[\"LandingNavbar.useEffect\"], [\n        dropdownOpen\n    ]);\n    // Initialize instant navigation\n    (0,_hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation)();\n    // Check authentication state specifically for landing page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const checkAuth = {\n                \"LandingNavbar.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { user } } = await supabase.auth.getUser();\n                        console.log('Landing navbar auth check:', !!user);\n                        setIsAuthenticated(!!user);\n                    } catch (error) {\n                        console.error('Landing navbar auth error:', error);\n                        setIsAuthenticated(false);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.checkAuth\"];\n            checkAuth();\n            // Listen for auth changes\n            const setupAuthListener = {\n                \"LandingNavbar.useEffect.setupAuthListener\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                            \"LandingNavbar.useEffect.setupAuthListener\": (event, session)=>{\n                                console.log('Landing navbar auth change:', event, !!session);\n                                setIsAuthenticated(!!(session === null || session === void 0 ? void 0 : session.user));\n                            }\n                        }[\"LandingNavbar.useEffect.setupAuthListener\"]);\n                        return ({\n                            \"LandingNavbar.useEffect.setupAuthListener\": ()=>subscription.unsubscribe()\n                        })[\"LandingNavbar.useEffect.setupAuthListener\"];\n                    } catch (error) {\n                        console.error('Landing navbar auth listener error:', error);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.setupAuthListener\"];\n            const cleanup = setupAuthListener();\n            return ({\n                \"LandingNavbar.useEffect\": ()=>{\n                    cleanup === null || cleanup === void 0 ? void 0 : cleanup.then({\n                        \"LandingNavbar.useEffect\": (fn)=>fn === null || fn === void 0 ? void 0 : fn()\n                    }[\"LandingNavbar.useEffect\"]);\n                }\n            })[\"LandingNavbar.useEffect\"];\n        }\n    }[\"LandingNavbar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-md border-b border-gray-800\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/RouKey_Logo_NOGLOW.png\",\n                                                    alt: \"RouKey\",\n                                                    width: 32,\n                                                    height: 32,\n                                                    className: \"h-8 w-8 transition-opacity duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/RouKey_Logo_GLOW.png\",\n                                                    alt: \"RouKey Glow\",\n                                                    width: 32,\n                                                    height: 32,\n                                                    className: \"h-8 w-8 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xl font-semibold text-white\",\n                                            children: \"RouKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('features');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'features' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/features\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Product overview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/integrations\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Integrations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/templates\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Templates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/ai\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"AI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/routing-strategies\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Routing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('usecases');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Use cases\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'usecases' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/it-ops\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"IT Operations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/security\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Security Operations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/development\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Development\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/sales\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Sales & Marketing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('docs');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Docs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'docs' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Overview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/features\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/api\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"API Reference\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/use-cases\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Use Cases\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/faq\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"FAQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/community\",\n                                            className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: \"Community\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/enterprise\",\n                                            className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: \"Enterprise\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/pricing\",\n                                            className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors font-medium\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-2.5 rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-orange-500/25\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"md:hidden py-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/features\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/routing-strategies\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Routing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/about\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/contact\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Docs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this),\n                            !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 279,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingNavbar, \"ky4PhVomv2TDsKmHya/yQXU3GCE=\", false, function() {\n    return [\n        _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation\n    ];\n});\n_c = LandingNavbar;\nvar _c;\n$RefreshReg$(_c, \"LandingNavbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\n"));

/***/ })

});