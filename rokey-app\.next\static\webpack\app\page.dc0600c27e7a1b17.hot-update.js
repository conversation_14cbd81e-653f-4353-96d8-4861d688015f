"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/LandingNavbar.tsx":
/*!**************************************************!*\
  !*** ./src/components/landing/LandingNavbar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* harmony import */ var _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInstantNavigation */ \"(app-pages-browser)/./src/hooks/useInstantNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LandingNavbar() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dropdownOpen, setDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const toggleDropdown = (dropdown)=>{\n        setDropdownOpen(dropdownOpen === dropdown ? null : dropdown);\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"LandingNavbar.useEffect.handleClickOutside\": ()=>{\n                    setDropdownOpen(null);\n                }\n            }[\"LandingNavbar.useEffect.handleClickOutside\"];\n            if (dropdownOpen) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"LandingNavbar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"LandingNavbar.useEffect\"];\n            }\n        }\n    }[\"LandingNavbar.useEffect\"], [\n        dropdownOpen\n    ]);\n    // Initialize instant navigation\n    (0,_hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation)();\n    // Check authentication state specifically for landing page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const checkAuth = {\n                \"LandingNavbar.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { user } } = await supabase.auth.getUser();\n                        console.log('Landing navbar auth check:', !!user);\n                        setIsAuthenticated(!!user);\n                    } catch (error) {\n                        console.error('Landing navbar auth error:', error);\n                        setIsAuthenticated(false);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.checkAuth\"];\n            checkAuth();\n            // Listen for auth changes\n            const setupAuthListener = {\n                \"LandingNavbar.useEffect.setupAuthListener\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                            \"LandingNavbar.useEffect.setupAuthListener\": (event, session)=>{\n                                console.log('Landing navbar auth change:', event, !!session);\n                                setIsAuthenticated(!!(session === null || session === void 0 ? void 0 : session.user));\n                            }\n                        }[\"LandingNavbar.useEffect.setupAuthListener\"]);\n                        return ({\n                            \"LandingNavbar.useEffect.setupAuthListener\": ()=>subscription.unsubscribe()\n                        })[\"LandingNavbar.useEffect.setupAuthListener\"];\n                    } catch (error) {\n                        console.error('Landing navbar auth listener error:', error);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.setupAuthListener\"];\n            const cleanup = setupAuthListener();\n            return ({\n                \"LandingNavbar.useEffect\": ()=>{\n                    cleanup === null || cleanup === void 0 ? void 0 : cleanup.then({\n                        \"LandingNavbar.useEffect\": (fn)=>fn === null || fn === void 0 ? void 0 : fn()\n                    }[\"LandingNavbar.useEffect\"]);\n                }\n            })[\"LandingNavbar.useEffect\"];\n        }\n    }[\"LandingNavbar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-5xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/RouKey_Logo_NOGLOW.png\",\n                                                    alt: \"RouKey\",\n                                                    width: 40,\n                                                    height: 40,\n                                                    className: \"h-10 w-10 transition-opacity duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/RouKey_Logo_GLOW.png\",\n                                                    alt: \"RouKey Glow\",\n                                                    width: 40,\n                                                    height: 40,\n                                                    className: \"h-10 w-10 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"RouKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('features');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'features' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/features\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Product overview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/integrations\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Integrations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/templates\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Templates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/ai\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"AI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/routing-strategies\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Routing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('usecases');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Use cases\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'usecases' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/it-ops\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"IT Operations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/security\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Security Operations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/development\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Development\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/use-cases/sales\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Sales & Marketing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('docs');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Docs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'docs' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-gray-900/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Overview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/features\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/api\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"API Reference\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/use-cases\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Use Cases\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/faq\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"FAQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/community\",\n                                            className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: \"Community\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/enterprise\",\n                                            className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: \"Enterprise\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/pricing\",\n                                            className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 text-gray-400 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"112+\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                        children: \"Sign in\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"relative inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group overflow-hidden\",\n                                        style: {\n                                            background: 'linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #f59e0b 100%)',\n                                            boxShadow: '0 4px 15px rgba(245, 158, 11, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"md:hidden py-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/features\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/routing-strategies\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Routing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/about\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/contact\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Docs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this),\n                            !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingNavbar, \"ky4PhVomv2TDsKmHya/yQXU3GCE=\", false, function() {\n    return [\n        _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation\n    ];\n});\n_c = LandingNavbar;\nvar _c;\n$RefreshReg$(_c, \"LandingNavbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\n"));

/***/ })

});