'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, PlayIcon, SparklesIcon, BoltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import InstantLink from '@/components/ui/InstantLink';

export default function HeroSection() {
  const [activeFeatureCard, setActiveFeatureCard] = useState('itops');

  return (
    <>
      {/* Hero Section - N8N Style Rectangular */}
      <section className="relative h-[95vh] overflow-hidden" style={{
        backgroundImage: 'url(/Hero_Section_Background_Image.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}>
        {/* Subtle Grid Overlay with Glow */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.008)_1px,transparent_1px)] bg-[size:50px_50px]"
             style={{
               boxShadow: 'inset 0 0 100px rgba(255,255,255,0.02)'
             }} />

        {/* Dark overlay for text readability */}
        <div className="absolute inset-0 bg-black/40"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center h-full py-16">
            {/* Left Column - Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
              className="text-left"
            >
              {/* N8N-Inspired Hero Title */}
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
                Flexible AI workflow <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">automation</span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                  for technical teams
                </span>
              </h1>

              <p className="text-base md:text-lg text-gray-300 mb-8 leading-relaxed max-w-lg">
                Build with the precision of code or the speed of drag-n-drop. Host with
                on-prem control or in-the-cloud convenience. RouKey gives you more
                freedom to implement multi-step AI agents and integrate apps than
                any other tool.
              </p>

              {/* N8N-Style CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <InstantLink
                  href="/pricing"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-orange-500/25"
                >
                  Get started for free
                </InstantLink>

                <InstantLink
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 border border-white/20 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200"
                >
                  Talk to sales
                </InstantLink>
              </motion.div>
            </motion.div>

            {/* Right Column - Visual Element */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className="relative flex justify-center items-center mt-64"
            >
              {/* Lightning Bolt Style Visual - Moved Down */}
              <div className="relative">
                <div className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-orange-400 via-orange-500 to-orange-600 opacity-80">
                  ⚡
                </div>
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-orange-500/20 blur-xl">
                  ⚡
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Feature Cards Section - N8N Style */}
      <section className="relative bg-gray-900 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Feature Cards */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                {/* IT Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('itops')}
                  className={`p-4 rounded-lg border transition-all duration-200 text-left ${
                    activeFeatureCard === 'itops'
                      ? 'bg-gray-800/90 border-orange-500 text-white'
                      : 'bg-gray-900/50 border-gray-700 text-gray-300 hover:bg-gray-800/70'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">IT Ops <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>On-board new employees</span>
                  </div>
                </button>

                {/* Sec Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('secops')}
                  className={`p-4 rounded-lg border transition-all duration-200 text-left ${
                    activeFeatureCard === 'secops'
                      ? 'bg-gray-800/90 border-orange-500 text-white'
                      : 'bg-gray-900/50 border-gray-700 text-gray-300 hover:bg-gray-800/70'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">Sec Ops <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>Enrich security incident tickets</span>
                  </div>
                </button>

                {/* Dev Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('devops')}
                  className={`p-4 rounded-lg border transition-all duration-200 text-left ${
                    activeFeatureCard === 'devops'
                      ? 'bg-gray-800/90 border-orange-500 text-white'
                      : 'bg-gray-900/50 border-gray-700 text-gray-300 hover:bg-gray-800/70'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">Dev Ops <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>Convert natural language into API calls</span>
                  </div>
                </button>

                {/* Sales Card */}
                <button
                  onClick={() => setActiveFeatureCard('sales')}
                  className={`p-4 rounded-lg border transition-all duration-200 text-left ${
                    activeFeatureCard === 'sales'
                      ? 'bg-gray-800/90 border-orange-500 text-white'
                      : 'bg-gray-900/50 border-gray-700 text-gray-300 hover:bg-gray-800/70'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">Sales <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>Generate customer insights from reviews</span>
                  </div>
                </button>

                {/* You Card */}
                <button
                  onClick={() => setActiveFeatureCard('you')}
                  className={`p-4 rounded-lg border transition-all duration-200 text-left ${
                    activeFeatureCard === 'you'
                      ? 'bg-gray-800/90 border-orange-500 text-white'
                      : 'bg-gray-900/50 border-gray-700 text-gray-300 hover:bg-gray-800/70'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">You <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-blue-400 mr-1">▶️</span>
                    <span>Watch this video to hear our pitch</span>
                  </div>
                </button>
              </div>
            </div>

            {/* Workflow Visualization */}
            <div className="relative">
              <div className="bg-gray-900/80 backdrop-blur-sm rounded-lg border border-gray-700 p-6">
                {activeFeatureCard === 'itops' && (
                  <Image
                    src="/Example_1.png"
                    alt="RouKey Workflow Example"
                    width={600}
                    height={400}
                    className="w-full h-auto rounded-lg"
                  />
                )}
                {activeFeatureCard !== 'itops' && (
                  <div className="w-full h-64 bg-gray-800 rounded-lg flex items-center justify-center">
                    <div className="text-gray-400 text-center">
                      <div className="text-lg font-medium mb-2">Workflow Visualization</div>
                      <div className="text-sm">Coming Soon for {activeFeatureCard}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
}
