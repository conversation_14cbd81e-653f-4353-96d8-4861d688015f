'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRightIcon, PlayIcon, SparklesIcon, BoltIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { useState, useEffect } from 'react';
import InstantLink from '@/components/ui/InstantLink';

export default function HeroSection() {
  const [activeFeatureCard, setActiveFeatureCard] = useState('itops');

  return (
    <>
      {/* Hero Section - N8N Style Rectangular */}
      <section className="relative h-[95vh] overflow-hidden" style={{
        backgroundImage: 'url(/Hero_Section_Background_Image.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}>
        {/* Subtle Grid Overlay with Glow */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.008)_1px,transparent_1px)] bg-[size:50px_50px]"
             style={{
               boxShadow: 'inset 0 0 100px rgba(255,255,255,0.02)'
             }} />

        {/* Dark overlay for text readability */}
        <div className="absolute inset-0 bg-black/40"></div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center h-full py-16">
            {/* Left Column - Content */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4 }}
              className="text-left"
            >
              {/* RouKey Hero Title */}
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 leading-tight">
                Universal AI <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">gateway</span>
                <br />
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                  with your own keys
                </span>
              </h1>

              <p className="text-base md:text-lg text-gray-300 mb-8 leading-relaxed max-w-lg">
                Connect to 50+ AI providers using your own API keys. Smart routing,
                fallback protection, and cost optimization. One unified interface
                for OpenAI, Claude, Gemini, and more - all with complete control
                over your data and costs.
              </p>

              {/* N8N-Style CTA Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.2 }}
                className="flex flex-col sm:flex-row gap-4"
              >
                <InstantLink
                  href="/pricing"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-orange-500/25"
                >
                  Get started for free
                </InstantLink>

                <InstantLink
                  href="/contact"
                  className="inline-flex items-center px-6 py-3 border border-white/20 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200"
                >
                  Talk to sales
                </InstantLink>
              </motion.div>
            </motion.div>

            {/* Right Column - Visual Element */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
              className="relative flex justify-center items-center mt-64"
            >
              {/* Lightning Bolt Style Visual - Enhanced with Shine & Gloss */}
              <div className="relative">
                {/* Main Lightning Bolt with Enhanced Gradient */}
                <div className="text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-400 via-orange-400 via-orange-500 to-orange-600 opacity-95 drop-shadow-2xl">
                  ⚡
                </div>

                {/* Glossy Highlight Overlay - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-200 via-orange-300 to-transparent opacity-40">
                  ⚡
                </div>

                {/* Outer Glow - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-orange-400/50 blur-2xl">
                  ⚡
                </div>

                {/* Inner Glow - Brighter */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-yellow-400/45 blur-lg">
                  ⚡
                </div>

                {/* Shine Effect - Brighter Animation */}
                <div className="absolute inset-0 text-8xl md:text-9xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-transparent via-yellow-300 to-transparent opacity-30 animate-pulse">
                  ⚡
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Feature Cards Section - N8N Style Seamless Transition */}
      <section className="relative bg-gray-900 -mt-32 pt-16">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Single unified 3D container like N8N */}
          <div className="relative bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-3xl border border-gray-600/50 shadow-2xl overflow-hidden">
            {/* Inner glow effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/5 to-transparent rounded-3xl"></div>

            {/* 3D depth effect */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-3xl"></div>

            {/* Feature Cards at the TOP of the container */}
            <div className="relative p-6 border-b border-gray-700/50">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-3">
                {/* IT Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('itops')}
                  className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                    activeFeatureCard === 'itops'
                      ? 'bg-gray-700/90 border-orange-500 text-white shadow-lg shadow-orange-500/20'
                      : 'bg-gray-800/50 border-gray-600/50 text-gray-300 hover:bg-gray-700/70 hover:border-gray-500'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">IT Ops <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>On-board new employees</span>
                  </div>
                </button>

                {/* Sec Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('secops')}
                  className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                    activeFeatureCard === 'secops'
                      ? 'bg-gray-700/90 border-orange-500 text-white shadow-lg shadow-orange-500/20'
                      : 'bg-gray-800/50 border-gray-600/50 text-gray-300 hover:bg-gray-700/70 hover:border-gray-500'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">Sec Ops <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>Enrich security incident tickets</span>
                  </div>
                </button>

                {/* Dev Ops Card */}
                <button
                  onClick={() => setActiveFeatureCard('devops')}
                  className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                    activeFeatureCard === 'devops'
                      ? 'bg-gray-700/90 border-orange-500 text-white shadow-lg shadow-orange-500/20'
                      : 'bg-gray-800/50 border-gray-600/50 text-gray-300 hover:bg-gray-700/70 hover:border-gray-500'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">Dev Ops <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>Convert natural language into API calls</span>
                  </div>
                </button>

                {/* Sales Card */}
                <button
                  onClick={() => setActiveFeatureCard('sales')}
                  className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                    activeFeatureCard === 'sales'
                      ? 'bg-gray-700/90 border-orange-500 text-white shadow-lg shadow-orange-500/20'
                      : 'bg-gray-800/50 border-gray-600/50 text-gray-300 hover:bg-gray-700/70 hover:border-gray-500'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">Sales <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-orange-400 mr-1">⚡</span>
                    <span>Generate customer insights from reviews</span>
                  </div>
                </button>

                {/* You Card */}
                <button
                  onClick={() => setActiveFeatureCard('you')}
                  className={`p-4 rounded-xl border transition-all duration-200 text-left ${
                    activeFeatureCard === 'you'
                      ? 'bg-gray-700/90 border-orange-500 text-white shadow-lg shadow-orange-500/20'
                      : 'bg-gray-800/50 border-gray-600/50 text-gray-300 hover:bg-gray-700/70 hover:border-gray-500'
                  }`}
                >
                  <div className="text-sm font-medium mb-1">You <span className="text-gray-400">can</span></div>
                  <div className="flex items-center text-xs">
                    <span className="text-blue-400 mr-1">▶️</span>
                    <span>Watch this video to hear our pitch</span>
                  </div>
                </button>
              </div>
            </div>

            {/* Workflow Visualization in the BOTTOM part of the same container */}
            <div className="relative p-8">
              <div className="aspect-[16/10] w-full">
                {activeFeatureCard === 'itops' && (
                  <div className="w-full h-full relative">
                    <Image
                      src="/Example_1.png"
                      alt="RouKey Workflow Example"
                      width={800}
                      height={500}
                      className="w-full h-full object-contain rounded-xl shadow-lg"
                    />
                    {/* Subtle overlay for better integration */}
                    <div className="absolute inset-0 bg-gradient-to-t from-gray-900/20 to-transparent rounded-xl pointer-events-none"></div>
                  </div>
                )}
                {activeFeatureCard !== 'itops' && (
                  <div className="w-full h-full bg-gradient-to-br from-gray-800/50 to-gray-900/50 rounded-xl border border-gray-700/50 flex items-center justify-center backdrop-blur-sm">
                    <div className="text-gray-300 text-center">
                      <div className="text-2xl font-semibold mb-4 text-white">Workflow Visualization</div>
                      <div className="text-lg text-gray-400">Coming Soon for {activeFeatureCard}</div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Bottom highlight for 3D effect */}
            <div className="absolute bottom-0 left-6 right-6 h-px bg-gradient-to-r from-transparent via-gray-500/50 to-transparent"></div>
          </div>

          {/* Ambient glow around entire container */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-purple-500/10 to-transparent rounded-3xl blur-xl -z-10 scale-105"></div>
        </div>
      </section>


    </>
  );
}
