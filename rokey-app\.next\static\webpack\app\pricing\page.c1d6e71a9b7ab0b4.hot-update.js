"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pricing/page",{

/***/ "(app-pages-browser)/./src/components/landing/LandingNavbar.tsx":
/*!**************************************************!*\
  !*** ./src/components/landing/LandingNavbar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* harmony import */ var _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInstantNavigation */ \"(app-pages-browser)/./src/hooks/useInstantNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LandingNavbar() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dropdownOpen, setDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const toggleDropdown = (dropdown)=>{\n        setDropdownOpen(dropdownOpen === dropdown ? null : dropdown);\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"LandingNavbar.useEffect.handleClickOutside\": ()=>{\n                    setDropdownOpen(null);\n                }\n            }[\"LandingNavbar.useEffect.handleClickOutside\"];\n            if (dropdownOpen) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"LandingNavbar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"LandingNavbar.useEffect\"];\n            }\n        }\n    }[\"LandingNavbar.useEffect\"], [\n        dropdownOpen\n    ]);\n    // Initialize instant navigation\n    (0,_hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation)();\n    // Check authentication state specifically for landing page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const checkAuth = {\n                \"LandingNavbar.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { user } } = await supabase.auth.getUser();\n                        console.log('Landing navbar auth check:', !!user);\n                        setIsAuthenticated(!!user);\n                    } catch (error) {\n                        console.error('Landing navbar auth error:', error);\n                        setIsAuthenticated(false);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.checkAuth\"];\n            checkAuth();\n            // Listen for auth changes\n            const setupAuthListener = {\n                \"LandingNavbar.useEffect.setupAuthListener\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                            \"LandingNavbar.useEffect.setupAuthListener\": (event, session)=>{\n                                console.log('Landing navbar auth change:', event, !!session);\n                                setIsAuthenticated(!!(session === null || session === void 0 ? void 0 : session.user));\n                            }\n                        }[\"LandingNavbar.useEffect.setupAuthListener\"]);\n                        return ({\n                            \"LandingNavbar.useEffect.setupAuthListener\": ()=>subscription.unsubscribe()\n                        })[\"LandingNavbar.useEffect.setupAuthListener\"];\n                    } catch (error) {\n                        console.error('Landing navbar auth listener error:', error);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.setupAuthListener\"];\n            const cleanup = setupAuthListener();\n            return ({\n                \"LandingNavbar.useEffect\": ()=>{\n                    cleanup === null || cleanup === void 0 ? void 0 : cleanup.then({\n                        \"LandingNavbar.useEffect\": (fn)=>fn === null || fn === void 0 ? void 0 : fn()\n                    }[\"LandingNavbar.useEffect\"]);\n                }\n            })[\"LandingNavbar.useEffect\"];\n        }\n    }[\"LandingNavbar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-4 left-1/2 transform -translate-x-1/2 z-50 bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-5xl mx-auto px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between h-14\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-3 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/RouKey_Logo_NOGLOW.png\",\n                                                    alt: \"RouKey\",\n                                                    width: 40,\n                                                    height: 40,\n                                                    className: \"h-10 w-10 transition-opacity duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    src: \"/RouKey_Logo_GLOW.png\",\n                                                    alt: \"RouKey Glow\",\n                                                    width: 40,\n                                                    height: 40,\n                                                    className: \"h-10 w-10 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-white\",\n                                            children: \"RouKey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden md:flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('features');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'features' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/features\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Product overview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/integrations\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Integrations\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/templates\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Templates\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/ai\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"AI\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/routing-strategies\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Routing\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('docs');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Docs\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'docs' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Overview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 158,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/features\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/api\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"API Reference\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/use-cases\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Use Cases\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 167,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/docs/faq\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"FAQ\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        toggleDropdown('about');\n                                                    },\n                                                    className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"About\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 17\n                                                }, this),\n                                                dropdownOpen === 'about' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/about-developer\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"About Developer\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/about\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"About RouKey\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            href: \"/contact\",\n                                                            className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                            children: \"Contact\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            href: \"/pricing\",\n                                            className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: \"Pricing\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:flex items-center space-x-4\",\n                            children: !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"relative inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group overflow-hidden\",\n                                        style: {\n                                            background: 'linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #f59e0b 100%)',\n                                            boxShadow: '0 4px 15px rgba(245, 158, 11, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                className: \"text-gray-300 hover:text-white transition-colors\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 251,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        y: -10\n                    },\n                    className: \"md:hidden py-4 border-t border-gray-700\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/features\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Features\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/routing-strategies\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Routing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/pricing\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Pricing\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/about\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"About\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/contact\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/docs\",\n                                className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                children: \"Docs\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 15\n                            }, this),\n                            !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold\",\n                                        children: \"Get Started\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 267,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingNavbar, \"ky4PhVomv2TDsKmHya/yQXU3GCE=\", false, function() {\n    return [\n        _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation\n    ];\n});\n_c = LandingNavbar;\nvar _c;\n$RefreshReg$(_c, \"LandingNavbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\n"));

/***/ })

});