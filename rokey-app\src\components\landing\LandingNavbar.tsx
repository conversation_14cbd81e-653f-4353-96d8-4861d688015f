'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { prefetcher } from '@/utils/cacheStrategy';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Bars3Icon, XMarkIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import InstantLink from '@/components/ui/InstantLink';
import { useInstantNavigation } from '@/hooks/useInstantNavigation';

export default function LandingNavbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);

  const toggleDropdown = (dropdown: string) => {
    setDropdownOpen(dropdownOpen === dropdown ? null : dropdown);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen(null);
    };

    if (dropdownOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [dropdownOpen]);

  // Initialize instant navigation
  useInstantNavigation();

  // Check authentication state specifically for landing page
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { createSupabaseBrowserClient } = await import('@/lib/supabase/client');
        const supabase = createSupabaseBrowserClient();

        const { data: { user } } = await supabase.auth.getUser();
        console.log('Landing navbar auth check:', !!user);
        setIsAuthenticated(!!user);
      } catch (error) {
        console.error('Landing navbar auth error:', error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Listen for auth changes
    const setupAuthListener = async () => {
      try {
        const { createSupabaseBrowserClient } = await import('@/lib/supabase/client');
        const supabase = createSupabaseBrowserClient();

        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, session) => {
            console.log('Landing navbar auth change:', event, !!session);
            setIsAuthenticated(!!session?.user);
          }
        );

        return () => subscription.unsubscribe();
      } catch (error) {
        console.error('Landing navbar auth listener error:', error);
      }
    };

    const cleanup = setupAuthListener();
    return () => {
      cleanup?.then(fn => fn?.());
    };
  }, []);

  return (
    <nav className="fixed top-4 left-0 right-0 z-50 px-6">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        {/* Left - Logo in its own container */}
        <div className="bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-2">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Image
                src="/RouKey_Logo_NOGLOW.png"
                alt="RouKey"
                width={40}
                height={40}
                className="h-10 w-10 transition-opacity duration-200"
              />
              <Image
                src="/RouKey_Logo_GLOW.png"
                alt="RouKey Glow"
                width={40}
                height={40}
                className="h-10 w-10 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              />
            </div>
            <span className="text-2xl font-bold text-white">RouKey</span>
          </Link>
        </div>

        {/* Center - Navigation in its own container */}
        <div className="bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-6 py-2">
          <div className="flex items-center space-x-6">

            {/* Features Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDropdown('features');
                }}
                className="flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium"
              >
                <span>Features</span>
                <ChevronDownIcon className="w-3 h-3" />
              </button>
              {dropdownOpen === 'features' && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50">
                  <InstantLink href="/features" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Product overview
                  </InstantLink>
                  <InstantLink href="/integrations" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Integrations
                  </InstantLink>
                  <InstantLink href="/templates" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Templates
                  </InstantLink>
                  <InstantLink href="/ai" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    AI
                  </InstantLink>
                  <InstantLink href="/routing-strategies" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Routing
                  </InstantLink>
                </div>
              )}
            </div>

            {/* Docs Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDropdown('docs');
                }}
                className="flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium"
              >
                <span>Docs</span>
                <ChevronDownIcon className="w-3 h-3" />
              </button>
              {dropdownOpen === 'docs' && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50">
                  <InstantLink href="/docs" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Overview
                  </InstantLink>
                  <InstantLink href="/docs/features" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Features
                  </InstantLink>
                  <InstantLink href="/docs/api" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    API Reference
                  </InstantLink>
                  <InstantLink href="/docs/use-cases" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Use Cases
                  </InstantLink>
                  <InstantLink href="/docs/faq" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    FAQ
                  </InstantLink>
                </div>
              )}
            </div>

            {/* About Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDropdown('about');
                }}
                className="flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium"
              >
                <span>About</span>
                <ChevronDownIcon className="w-3 h-3" />
              </button>
              {dropdownOpen === 'about' && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50">
                  <InstantLink href="/about-developer" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    About Developer
                  </InstantLink>
                  <InstantLink href="/about" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    About RouKey
                  </InstantLink>
                  <InstantLink href="/contact" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Contact
                  </InstantLink>
                </div>
              )}
            </div>

            {/* Pricing */}
            <InstantLink
              href="/pricing"
              className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
            >
              Pricing
            </InstantLink>
          </div>
        </div>

        {/* Right - Auth buttons in their own container */}
        <div className="bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-2">
          <div className="flex items-center space-x-3">
            {!loading && (
              isAuthenticated ? (
                <InstantLink
                  href="/dashboard"
                  className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold"
                >
                  Dashboard
                </InstantLink>
              ) : (
                <>
                  <InstantLink
                    href="/auth/signin"
                    className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
                  >
                    Sign In
                  </InstantLink>

                  {/* N8N-Style Get Started Button */}
                  <InstantLink
                    href="/pricing"
                    className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-md shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group overflow-hidden"
                    style={{
                      background: 'linear-gradient(135deg, #f59e0b 0%, #f97316 50%, #f59e0b 100%)',
                      boxShadow: '0 4px 15px rgba(245, 158, 11, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                    }}
                  >
                    <span className="relative z-10">Get Started</span>
                    <div className="absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200"></div>
                  </InstantLink>
                </>
              )
            )}
          </div>
        </div>
      </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-gray-300 hover:text-white transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="md:hidden py-4 border-t border-gray-700"
          >
            <div className="flex flex-col space-y-4">
              <InstantLink href="/features" className="text-gray-300 hover:text-white transition-colors duration-100">
                Features
              </InstantLink>
              <InstantLink href="/routing-strategies" className="text-gray-300 hover:text-white transition-colors duration-100">
                Routing
              </InstantLink>
              <InstantLink href="/pricing" className="text-gray-300 hover:text-white transition-colors duration-100">
                Pricing
              </InstantLink>
              <InstantLink href="/about" className="text-gray-300 hover:text-white transition-colors duration-100">
                About
              </InstantLink>
              <InstantLink href="/contact" className="text-gray-300 hover:text-white transition-colors duration-100">
                Contact
              </InstantLink>
              <InstantLink href="/docs" className="text-gray-300 hover:text-white transition-colors duration-100">
                Docs
              </InstantLink>
              {!loading && (
                isAuthenticated ? (
                  <InstantLink
                    href="/dashboard"
                    className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold"
                  >
                    Dashboard
                  </InstantLink>
                ) : (
                  <>
                    <InstantLink href="/auth/signin" className="text-gray-300 hover:text-white transition-colors duration-100">
                      Sign In
                    </InstantLink>
                    <InstantLink
                      href="/pricing"
                      className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-4 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 text-center font-semibold"
                    >
                      Get Started
                    </InstantLink>
                  </>
                )
              )}
            </div>
          </motion.div>
        )}
      </div>
    </nav>
  );
}
