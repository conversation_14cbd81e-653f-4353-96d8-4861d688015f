"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/landing/LandingNavbar.tsx":
/*!**************************************************!*\
  !*** ./src/components/landing/LandingNavbar.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingNavbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bars3Icon,ChevronDownIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* harmony import */ var _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useInstantNavigation */ \"(app-pages-browser)/./src/hooks/useInstantNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LandingNavbar() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [dropdownOpen, setDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const toggleDropdown = (dropdown)=>{\n        setDropdownOpen(dropdownOpen === dropdown ? null : dropdown);\n    };\n    const handleSignOut = async ()=>{\n        try {\n            const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n            const supabase = createSupabaseBrowserClient();\n            await supabase.auth.signOut();\n            setIsAuthenticated(false);\n        } catch (error) {\n            console.error('Sign out error:', error);\n        }\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"LandingNavbar.useEffect.handleClickOutside\": ()=>{\n                    setDropdownOpen(null);\n                }\n            }[\"LandingNavbar.useEffect.handleClickOutside\"];\n            if (dropdownOpen) {\n                document.addEventListener('click', handleClickOutside);\n                return ({\n                    \"LandingNavbar.useEffect\": ()=>document.removeEventListener('click', handleClickOutside)\n                })[\"LandingNavbar.useEffect\"];\n            }\n        }\n    }[\"LandingNavbar.useEffect\"], [\n        dropdownOpen\n    ]);\n    // Initialize instant navigation\n    (0,_hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation)();\n    // Check authentication state specifically for landing page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingNavbar.useEffect\": ()=>{\n            const checkAuth = {\n                \"LandingNavbar.useEffect.checkAuth\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { user } } = await supabase.auth.getUser();\n                        console.log('Landing navbar auth check:', !!user);\n                        setIsAuthenticated(!!user);\n                    } catch (error) {\n                        console.error('Landing navbar auth error:', error);\n                        setIsAuthenticated(false);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.checkAuth\"];\n            checkAuth();\n            // Listen for auth changes\n            const setupAuthListener = {\n                \"LandingNavbar.useEffect.setupAuthListener\": async ()=>{\n                    try {\n                        const { createSupabaseBrowserClient } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_supabase_client_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/supabase/client */ \"(app-pages-browser)/./src/lib/supabase/client.ts\"));\n                        const supabase = createSupabaseBrowserClient();\n                        const { data: { subscription } } = supabase.auth.onAuthStateChange({\n                            \"LandingNavbar.useEffect.setupAuthListener\": (event, session)=>{\n                                console.log('Landing navbar auth change:', event, !!session);\n                                setIsAuthenticated(!!(session === null || session === void 0 ? void 0 : session.user));\n                            }\n                        }[\"LandingNavbar.useEffect.setupAuthListener\"]);\n                        return ({\n                            \"LandingNavbar.useEffect.setupAuthListener\": ()=>subscription.unsubscribe()\n                        })[\"LandingNavbar.useEffect.setupAuthListener\"];\n                    } catch (error) {\n                        console.error('Landing navbar auth listener error:', error);\n                    }\n                }\n            }[\"LandingNavbar.useEffect.setupAuthListener\"];\n            const cleanup = setupAuthListener();\n            return ({\n                \"LandingNavbar.useEffect\": ()=>{\n                    cleanup === null || cleanup === void 0 ? void 0 : cleanup.then({\n                        \"LandingNavbar.useEffect\": (fn)=>fn === null || fn === void 0 ? void 0 : fn()\n                    }[\"LandingNavbar.useEffect\"]);\n                }\n            })[\"LandingNavbar.useEffect\"];\n        }\n    }[\"LandingNavbar.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-4 left-0 right-0 z-50 px-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between max-w-7xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"flex items-center space-x-3 group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/RouKey_Logo_NOGLOW.png\",\n                                            alt: \"RouKey\",\n                                            width: 40,\n                                            height: 40,\n                                            className: \"h-10 w-10 transition-opacity duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            src: \"/RouKey_Logo_GLOW.png\",\n                                            alt: \"RouKey Glow\",\n                                            width: 40,\n                                            height: 40,\n                                            className: \"h-10 w-10 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl font-bold text-white\",\n                                    children: \"RouKey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-6 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                toggleDropdown('features');\n                                            },\n                                            className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        dropdownOpen === 'features' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/features\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Product overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 136,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/integrations\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Integrations\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/templates\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Templates\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/ai\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/routing-strategies\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Routing\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                toggleDropdown('docs');\n                                            },\n                                            className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Docs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        dropdownOpen === 'docs' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Overview\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs/features\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Features\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs/api\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"API Reference\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs/use-cases\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Use Cases\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/docs/faq\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"FAQ\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                toggleDropdown('about');\n                                            },\n                                            className: \"flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"About\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        dropdownOpen === 'about' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/about-developer\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"About Developer\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/about\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"About RouKey\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/contact\",\n                                                    className: \"block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm\",\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                    children: \"Pricing\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/dashboard\",\n                                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-gray-300 hover:text-white transition-colors text-sm font-medium\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        href: \"/pricing\",\n                                        className: \"relative inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group overflow-hidden\",\n                                        style: {\n                                            background: 'linear-gradient(135deg, #ff6b35 0%, #f97316 50%, #ff4500 100%)',\n                                            boxShadow: '0 4px 15px rgba(255, 107, 53, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"relative z-10\",\n                                                children: \"Get Started\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-r from-orange-400 via-red-500 to-orange-600 opacity-0 group-hover:opacity-80 transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 group-hover:via-white/20 group-hover:to-white/30 transition-all duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:hidden bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                            className: \"text-gray-300 hover:text-white transition-colors\",\n                            children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bars3Icon_ChevronDownIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: -10\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                exit: {\n                    opacity: 0,\n                    y: -10\n                },\n                className: \"md:hidden mt-4 bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl mx-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col space-y-4 px-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/features\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"Features\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/docs\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"Docs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/about\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"About\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/pricing\",\n                            className: \"text-gray-300 hover:text-white transition-colors duration-100\",\n                            children: \"Pricing\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 13\n                        }, this),\n                        !loading && (isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/dashboard\",\n                            className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold text-center\",\n                            children: \"Dashboard\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/auth/signin\",\n                                    className: \"text-gray-300 hover:text-white transition-colors text-center\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 text-white px-6 py-2 rounded-lg text-center font-medium\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 17\n                        }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n                lineNumber: 281,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\landing\\\\LandingNavbar.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingNavbar, \"ky4PhVomv2TDsKmHya/yQXU3GCE=\", false, function() {\n    return [\n        _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_5__.useInstantNavigation\n    ];\n});\n_c = LandingNavbar;\nvar _c;\n$RefreshReg$(_c, \"LandingNavbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\n"));

/***/ })

});