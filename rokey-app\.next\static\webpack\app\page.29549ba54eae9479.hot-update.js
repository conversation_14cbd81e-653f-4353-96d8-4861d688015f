"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LandingPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/serviceWorker */ \"(app-pages-browser)/./src/utils/serviceWorker.ts\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/landing/HeroSection */ \"(app-pages-browser)/./src/components/landing/HeroSection.tsx\");\n/* harmony import */ var _components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/landing/FeaturesSection */ \"(app-pages-browser)/./src/components/landing/FeaturesSection.tsx\");\n/* harmony import */ var _components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/landing/RoutingVisualization */ \"(app-pages-browser)/./src/components/landing/RoutingVisualization.tsx\");\n/* harmony import */ var _components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/landing/TestimonialsSection */ \"(app-pages-browser)/./src/components/landing/TestimonialsSection.tsx\");\n/* harmony import */ var _components_landing_CTASection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/landing/CTASection */ \"(app-pages-browser)/./src/components/landing/CTASection.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/landing/EnhancedGridBackground */ \"(app-pages-browser)/./src/components/landing/EnhancedGridBackground.tsx\");\n/* harmony import */ var _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useInstantNavigation */ \"(app-pages-browser)/./src/hooks/useInstantNavigation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize instant navigation for all pages\n    (0,_hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_11__.useInstantNavigation)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            // Register service worker for caching\n            (0,_utils_serviceWorker__WEBPACK_IMPORTED_MODULE_2__.registerServiceWorker)({\n                onSuccess: {\n                    \"LandingPage.useEffect\": ()=>console.log('✅ Service Worker registered for caching')\n                }[\"LandingPage.useEffect\"],\n                onUpdate: {\n                    \"LandingPage.useEffect\": ()=>console.log('🔄 New content available')\n                }[\"LandingPage.useEffect\"],\n                onError: {\n                    \"LandingPage.useEffect\": (error)=>console.warn('⚠️ Service Worker registration failed:', error)\n                }[\"LandingPage.useEffect\"]\n            });\n            setIsLoaded(true);\n        }\n    }[\"LandingPage.useEffect\"], []);\n    // Prefetch likely next pages on hover\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LandingPage.useEffect\": ()=>{\n            if (true) {\n                const prefetchPages = {\n                    \"LandingPage.useEffect.prefetchPages\": ()=>{\n                        // Prefetch auth pages since users likely to sign up\n                        const link1 = document.createElement('link');\n                        link1.rel = 'prefetch';\n                        link1.href = '/pricing';\n                        document.head.appendChild(link1);\n                        const link2 = document.createElement('link');\n                        link2.rel = 'prefetch';\n                        link2.href = '/auth/signup';\n                        document.head.appendChild(link2);\n                        const link3 = document.createElement('link');\n                        link3.rel = 'prefetch';\n                        link3.href = '/features';\n                        document.head.appendChild(link3);\n                    }\n                }[\"LandingPage.useEffect.prefetchPages\"];\n                // Prefetch after initial load\n                const prefetchTimer = setTimeout(prefetchPages, 1000);\n                return ({\n                    \"LandingPage.useEffect\": ()=>clearTimeout(prefetchTimer)\n                })[\"LandingPage.useEffect\"];\n            }\n        }\n    }[\"LandingPage.useEffect\"], []);\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-white\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-black text-sm\",\n                        children: \"Loading RouKey...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen relative overflow-hidden\",\n        style: {\n            background: \"radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                gridSize: 60,\n                opacity: 0.032,\n                color: \"#000000\",\n                variant: \"subtle\",\n                animated: true,\n                className: \"fixed inset-0\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-screen relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        gridSize: 45,\n                        opacity: 0.024,\n                        color: \"#ff6b35\",\n                        variant: \"tech\",\n                        animated: true,\n                        glowEffect: true,\n                        className: \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-0\",\n                style: {\n                    top: '200vh'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-screen relative\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_EnhancedGridBackground__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        gridSize: 35,\n                        opacity: 0.056,\n                        color: \"#000000\",\n                        variant: \"premium\",\n                        animated: true,\n                        className: \"absolute inset-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_HeroSection__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_RoutingVisualization__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_FeaturesSection__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_TestimonialsSection__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_CTASection__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 115,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"uOAbuRw7Wn6sEsUgr5xbZeoFbLA=\", false, function() {\n    return [\n        _hooks_useInstantNavigation__WEBPACK_IMPORTED_MODULE_11__.useInstantNavigation\n    ];\n});\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU0QztBQUNrQjtBQUNDO0FBQ0o7QUFFUTtBQUNVO0FBQ0Y7QUFDbEI7QUFDUjtBQUNnQztBQUNiO0FBRXJELFNBQVNZOztJQUN0QixNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2QsK0NBQVFBLENBQUM7SUFFekMsOENBQThDO0lBQzlDVyxrRkFBb0JBO0lBRXBCVixnREFBU0E7aUNBQUM7WUFDUixzQ0FBc0M7WUFDdENDLDJFQUFxQkEsQ0FBQztnQkFDcEJhLFNBQVM7NkNBQUUsSUFBTUMsUUFBUUMsR0FBRyxDQUFDOztnQkFDN0JDLFFBQVE7NkNBQUUsSUFBTUYsUUFBUUMsR0FBRyxDQUFDOztnQkFDNUJFLE9BQU87NkNBQUUsQ0FBQ0MsUUFBVUosUUFBUUssSUFBSSxDQUFDLDBDQUEwQ0Q7O1lBQzdFO1lBRUFOLFlBQVk7UUFDZDtnQ0FBRyxFQUFFO0lBRUwsc0NBQXNDO0lBQ3RDYixnREFBU0E7aUNBQUM7WUFDUixJQUFJLElBQTZCLEVBQUU7Z0JBQ2pDLE1BQU1xQjsyREFBZ0I7d0JBQ3BCLG9EQUFvRDt3QkFDcEQsTUFBTUMsUUFBUUMsU0FBU0MsYUFBYSxDQUFDO3dCQUNyQ0YsTUFBTUcsR0FBRyxHQUFHO3dCQUNaSCxNQUFNSSxJQUFJLEdBQUc7d0JBQ2JILFNBQVNJLElBQUksQ0FBQ0MsV0FBVyxDQUFDTjt3QkFFMUIsTUFBTU8sUUFBUU4sU0FBU0MsYUFBYSxDQUFDO3dCQUNyQ0ssTUFBTUosR0FBRyxHQUFHO3dCQUNaSSxNQUFNSCxJQUFJLEdBQUc7d0JBQ2JILFNBQVNJLElBQUksQ0FBQ0MsV0FBVyxDQUFDQzt3QkFFMUIsTUFBTUMsUUFBUVAsU0FBU0MsYUFBYSxDQUFDO3dCQUNyQ00sTUFBTUwsR0FBRyxHQUFHO3dCQUNaSyxNQUFNSixJQUFJLEdBQUc7d0JBQ2JILFNBQVNJLElBQUksQ0FBQ0MsV0FBVyxDQUFDRTtvQkFDNUI7O2dCQUVBLDhCQUE4QjtnQkFDOUIsTUFBTUMsZ0JBQWdCQyxXQUFXWCxlQUFlO2dCQUNoRDs2Q0FBTyxJQUFNWSxhQUFhRjs7WUFDNUI7UUFDRjtnQ0FBRyxFQUFFO0lBRUwsSUFBSSxDQUFDbkIsVUFBVTtRQUNiLHFCQUNFLDhEQUFDc0I7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBRUQsV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSTFDO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlDLFdBQVU7UUFBd0NFLE9BQU87WUFDNURDLFlBQWE7UUFDZjs7MEJBRUUsOERBQUM3QixtRkFBc0JBO2dCQUNyQjhCLFVBQVU7Z0JBQ1ZDLFNBQVM7Z0JBQ1RDLE9BQU07Z0JBQ05DLFNBQVE7Z0JBQ1JDLFVBQVU7Z0JBQ1ZSLFdBQVU7Ozs7OzswQkFJWiw4REFBQ0Q7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDMUIsbUZBQXNCQTt3QkFDckI4QixVQUFVO3dCQUNWQyxTQUFTO3dCQUNUQyxPQUFNO3dCQUNOQyxTQUFRO3dCQUNSQyxVQUFVO3dCQUNWQyxZQUFZO3dCQUNaVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzBCQU1oQiw4REFBQ0Q7Z0JBQUlDLFdBQVU7Z0JBQXVCRSxPQUFPO29CQUFFUSxLQUFLO2dCQUFROzBCQUMxRCw0RUFBQ1g7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUMxQixtRkFBc0JBO3dCQUNyQjhCLFVBQVU7d0JBQ1ZDLFNBQVM7d0JBQ1RDLE9BQU07d0JBQ05DLFNBQVE7d0JBQ1JDLFVBQVU7d0JBQ1ZSLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWhCLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNqQyx5RUFBYUE7Ozs7O2tDQUVkLDhEQUFDNEM7OzBDQUNDLDhEQUFDM0MsdUVBQVdBOzs7OzswQ0FDWiw4REFBQ0UsZ0ZBQW9CQTs7Ozs7MENBQ3JCLDhEQUFDRCwyRUFBZUE7Ozs7OzBDQUNoQiw4REFBQ0UsK0VBQW1CQTs7Ozs7MENBQ3BCLDhEQUFDQyxzRUFBVUE7Ozs7Ozs7Ozs7O2tDQUdiLDhEQUFDQyxrRUFBTUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS2Y7R0FuSHdCRzs7UUFJdEJELDhFQUFvQkE7OztLQUpFQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGFwcFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgcmVnaXN0ZXJTZXJ2aWNlV29ya2VyIH0gZnJvbSAnQC91dGlscy9zZXJ2aWNlV29ya2VyJztcbmltcG9ydCBMYW5kaW5nTmF2YmFyIGZyb20gJ0AvY29tcG9uZW50cy9sYW5kaW5nL0xhbmRpbmdOYXZiYXInO1xuaW1wb3J0IEhlcm9TZWN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9sYW5kaW5nL0hlcm9TZWN0aW9uJztcbmltcG9ydCBUcnVzdEJhZGdlcyBmcm9tICdAL2NvbXBvbmVudHMvbGFuZGluZy9UcnVzdEJhZGdlcyc7XG5pbXBvcnQgRmVhdHVyZXNTZWN0aW9uIGZyb20gJ0AvY29tcG9uZW50cy9sYW5kaW5nL0ZlYXR1cmVzU2VjdGlvbic7XG5pbXBvcnQgUm91dGluZ1Zpc3VhbGl6YXRpb24gZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvUm91dGluZ1Zpc3VhbGl6YXRpb24nO1xuaW1wb3J0IFRlc3RpbW9uaWFsc1NlY3Rpb24gZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvVGVzdGltb25pYWxzU2VjdGlvbic7XG5pbXBvcnQgQ1RBU2VjdGlvbiBmcm9tICdAL2NvbXBvbmVudHMvbGFuZGluZy9DVEFTZWN0aW9uJztcbmltcG9ydCBGb290ZXIgZnJvbSAnQC9jb21wb25lbnRzL2xhbmRpbmcvRm9vdGVyJztcbmltcG9ydCBFbmhhbmNlZEdyaWRCYWNrZ3JvdW5kIGZyb20gJ0AvY29tcG9uZW50cy9sYW5kaW5nL0VuaGFuY2VkR3JpZEJhY2tncm91bmQnO1xuaW1wb3J0IHsgdXNlSW5zdGFudE5hdmlnYXRpb24gfSBmcm9tICdAL2hvb2tzL3VzZUluc3RhbnROYXZpZ2F0aW9uJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTGFuZGluZ1BhZ2UoKSB7XG4gIGNvbnN0IFtpc0xvYWRlZCwgc2V0SXNMb2FkZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIC8vIEluaXRpYWxpemUgaW5zdGFudCBuYXZpZ2F0aW9uIGZvciBhbGwgcGFnZXNcbiAgdXNlSW5zdGFudE5hdmlnYXRpb24oKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIFJlZ2lzdGVyIHNlcnZpY2Ugd29ya2VyIGZvciBjYWNoaW5nXG4gICAgcmVnaXN0ZXJTZXJ2aWNlV29ya2VyKHtcbiAgICAgIG9uU3VjY2VzczogKCkgPT4gY29uc29sZS5sb2coJ+KchSBTZXJ2aWNlIFdvcmtlciByZWdpc3RlcmVkIGZvciBjYWNoaW5nJyksXG4gICAgICBvblVwZGF0ZTogKCkgPT4gY29uc29sZS5sb2coJ/CflIQgTmV3IGNvbnRlbnQgYXZhaWxhYmxlJyksXG4gICAgICBvbkVycm9yOiAoZXJyb3IpID0+IGNvbnNvbGUud2Fybign4pqg77iPIFNlcnZpY2UgV29ya2VyIHJlZ2lzdHJhdGlvbiBmYWlsZWQ6JywgZXJyb3IpXG4gICAgfSk7XG5cbiAgICBzZXRJc0xvYWRlZCh0cnVlKTtcbiAgfSwgW10pO1xuXG4gIC8vIFByZWZldGNoIGxpa2VseSBuZXh0IHBhZ2VzIG9uIGhvdmVyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBjb25zdCBwcmVmZXRjaFBhZ2VzID0gKCkgPT4ge1xuICAgICAgICAvLyBQcmVmZXRjaCBhdXRoIHBhZ2VzIHNpbmNlIHVzZXJzIGxpa2VseSB0byBzaWduIHVwXG4gICAgICAgIGNvbnN0IGxpbmsxID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnbGluaycpO1xuICAgICAgICBsaW5rMS5yZWwgPSAncHJlZmV0Y2gnO1xuICAgICAgICBsaW5rMS5ocmVmID0gJy9wcmljaW5nJztcbiAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rMSk7XG5cbiAgICAgICAgY29uc3QgbGluazIgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdsaW5rJyk7XG4gICAgICAgIGxpbmsyLnJlbCA9ICdwcmVmZXRjaCc7XG4gICAgICAgIGxpbmsyLmhyZWYgPSAnL2F1dGgvc2lnbnVwJztcbiAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rMik7XG5cbiAgICAgICAgY29uc3QgbGluazMgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdsaW5rJyk7XG4gICAgICAgIGxpbmszLnJlbCA9ICdwcmVmZXRjaCc7XG4gICAgICAgIGxpbmszLmhyZWYgPSAnL2ZlYXR1cmVzJztcbiAgICAgICAgZG9jdW1lbnQuaGVhZC5hcHBlbmRDaGlsZChsaW5rMyk7XG4gICAgICB9O1xuXG4gICAgICAvLyBQcmVmZXRjaCBhZnRlciBpbml0aWFsIGxvYWRcbiAgICAgIGNvbnN0IHByZWZldGNoVGltZXIgPSBzZXRUaW1lb3V0KHByZWZldGNoUGFnZXMsIDEwMDApO1xuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dChwcmVmZXRjaFRpbWVyKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICBpZiAoIWlzTG9hZGVkKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1bI2ZmNmIzNV0gbXgtYXV0byBtYi0yXCI+PC9kaXY+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ibGFjayB0ZXh0LXNtXCI+TG9hZGluZyBSb3VLZXkuLi48L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gcmVsYXRpdmUgb3ZlcmZsb3ctaGlkZGVuXCIgc3R5bGU9e3tcbiAgICAgIGJhY2tncm91bmQ6IGByYWRpYWwtZ3JhZGllbnQoZWxsaXBzZSBhdCBjZW50ZXIsICMxQzA1MUMgMCUsICMwNDA3MTYgNzAlKWBcbiAgICB9fT5cbiAgICAgIHsvKiBNYWluIEdyaWQgQmFja2dyb3VuZCAtIFJlZHVjZWQgT3BhY2l0eSAqL31cbiAgICAgIDxFbmhhbmNlZEdyaWRCYWNrZ3JvdW5kXG4gICAgICAgIGdyaWRTaXplPXs2MH1cbiAgICAgICAgb3BhY2l0eT17MC4wMzJ9XG4gICAgICAgIGNvbG9yPVwiIzAwMDAwMFwiXG4gICAgICAgIHZhcmlhbnQ9XCJzdWJ0bGVcIlxuICAgICAgICBhbmltYXRlZD17dHJ1ZX1cbiAgICAgICAgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMFwiXG4gICAgICAvPlxuXG4gICAgICB7LyogVGVjaCBHcmlkIE92ZXJsYXkgZm9yIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCB6LTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlbiByZWxhdGl2ZVwiPlxuICAgICAgICAgIDxFbmhhbmNlZEdyaWRCYWNrZ3JvdW5kXG4gICAgICAgICAgICBncmlkU2l6ZT17NDV9XG4gICAgICAgICAgICBvcGFjaXR5PXswLjAyNH1cbiAgICAgICAgICAgIGNvbG9yPVwiI2ZmNmIzNVwiXG4gICAgICAgICAgICB2YXJpYW50PVwidGVjaFwiXG4gICAgICAgICAgICBhbmltYXRlZD17dHJ1ZX1cbiAgICAgICAgICAgIGdsb3dFZmZlY3Q9e3RydWV9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wXCJcbiAgICAgICAgICAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUHJlbWl1bSBHcmlkIGZvciBGZWF0dXJlcyBTZWN0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIHotMFwiIHN0eWxlPXt7IHRvcDogJzIwMHZoJyB9fT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLXNjcmVlbiByZWxhdGl2ZVwiPlxuICAgICAgICAgIDxFbmhhbmNlZEdyaWRCYWNrZ3JvdW5kXG4gICAgICAgICAgICBncmlkU2l6ZT17MzV9XG4gICAgICAgICAgICBvcGFjaXR5PXswLjA1Nn1cbiAgICAgICAgICAgIGNvbG9yPVwiIzAwMDAwMFwiXG4gICAgICAgICAgICB2YXJpYW50PVwicHJlbWl1bVwiXG4gICAgICAgICAgICBhbmltYXRlZD17dHJ1ZX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTBcIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBDb250ZW50ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwXCI+XG4gICAgICAgIDxMYW5kaW5nTmF2YmFyIC8+XG5cbiAgICAgICAgPG1haW4+XG4gICAgICAgICAgPEhlcm9TZWN0aW9uIC8+XG4gICAgICAgICAgPFJvdXRpbmdWaXN1YWxpemF0aW9uIC8+XG4gICAgICAgICAgPEZlYXR1cmVzU2VjdGlvbiAvPlxuICAgICAgICAgIDxUZXN0aW1vbmlhbHNTZWN0aW9uIC8+XG4gICAgICAgICAgPENUQVNlY3Rpb24gLz5cbiAgICAgICAgPC9tYWluPlxuXG4gICAgICAgIDxGb290ZXIgLz5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xuXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJyZWdpc3RlclNlcnZpY2VXb3JrZXIiLCJMYW5kaW5nTmF2YmFyIiwiSGVyb1NlY3Rpb24iLCJGZWF0dXJlc1NlY3Rpb24iLCJSb3V0aW5nVmlzdWFsaXphdGlvbiIsIlRlc3RpbW9uaWFsc1NlY3Rpb24iLCJDVEFTZWN0aW9uIiwiRm9vdGVyIiwiRW5oYW5jZWRHcmlkQmFja2dyb3VuZCIsInVzZUluc3RhbnROYXZpZ2F0aW9uIiwiTGFuZGluZ1BhZ2UiLCJpc0xvYWRlZCIsInNldElzTG9hZGVkIiwib25TdWNjZXNzIiwiY29uc29sZSIsImxvZyIsIm9uVXBkYXRlIiwib25FcnJvciIsImVycm9yIiwid2FybiIsInByZWZldGNoUGFnZXMiLCJsaW5rMSIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsInJlbCIsImhyZWYiLCJoZWFkIiwiYXBwZW5kQ2hpbGQiLCJsaW5rMiIsImxpbmszIiwicHJlZmV0Y2hUaW1lciIsInNldFRpbWVvdXQiLCJjbGVhclRpbWVvdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJwIiwic3R5bGUiLCJiYWNrZ3JvdW5kIiwiZ3JpZFNpemUiLCJvcGFjaXR5IiwiY29sb3IiLCJ2YXJpYW50IiwiYW5pbWF0ZWQiLCJnbG93RWZmZWN0IiwidG9wIiwibWFpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});